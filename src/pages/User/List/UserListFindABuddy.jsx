import React, { useEffect, useState, useContext } from "react";
import TreeSDK from "Utils/TreeSDK";
import { GlobalContext, showToast } from "Context/Global";
import { tokenExpireError, AuthContext } from "Context/Auth";
import MkdSDK from "Utils/MkdSDK";
import LoadingSpinner from "Components/LoadingSpinner";
import { IoChevronDown } from "react-icons/io5";
import FindBuddyTableTab from "Components/UserFindBuddy/FindBuddyTableTab";
import FindBuddyCalendarTab from "Components/UserFindBuddy/FindBuddyCalendarTab";
import FindBuddyWeeklyTab from "Components/UserFindBuddy/FindBuddyWeeklyTab";
import { Link } from "react-router-dom";
import SubscriptionsModal from "Components/UserFindBuddy/SubscriptionsModal";
import { useClub } from "Context/Club";
import AccessRestricted from "Components/Shared/AccessRestricted";

let tdk = new TreeSDK();
let sdk = new MkdSDK();

export default function UserListFindABuddy() {
  const [activeTab, setActiveTab] = useState(null);
  const [initialLoading, setInitialLoading] = useState(true);

  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { user_permissions } = useClub();
  const [isLoading, setLoading] = useState(false);
  const [selectedRequestTab, setSelectedRequestTab] = useState("all-requests");
  const [buddyData, setBuddyData] = useState([]);
  const [allBuddyData, setAllBuddyData] = useState([]);
  const [isSubscriptionModalOpen, setIsSubscriptionModalOpen] = useState(false);
  const [clubProfile, setClubProfile] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [clubSports, setClubSports] = useState([]);

  const tabs = [
    { id: "table", label: "Table" },
    { id: "calendar", label: "Calendar" },
    { id: "weekly", label: "Weekly" },
  ];

  const requestTabs = [
    {
      id: "all-requests",
      label: "All requests",
    },
    {
      id: "my-requests",
      label: "My requests",
    },
  ];

  const user_id = localStorage.getItem("user");

  const fetchData = async (
    pageNum,
    limitNum,
    filters = {},
    tab = "all-requests"
  ) => {
    setLoading(true);
    console.log("filters", filters);
    try {
      let url =
        tab === "all-requests"
          ? `/v3/api/custom/courtmatchup/user/buddy/all-requests`
          : `/v3/api/custom/courtmatchup/user/buddy/my-requests`;
      const queryParams = [];

      if (filters && typeof filters === "object") {
        Object.entries(filters).forEach(([key, value]) => {
          if (value) {
            queryParams.push(`${key}=${encodeURIComponent(value)}`);
          }
        });
      }

      if (queryParams.length > 0) {
        url += `?${queryParams.join("&")}`;
      }

      const buddyRequestResponse = await sdk.callRawAPI(url, {}, "GET");

      if (!buddyRequestResponse.error) {
        setLoading(false);

        // Add source field to each buddy object
        const processedData =
          tab === "all-requests"
            ? buddyRequestResponse.list.map((buddy) => ({
                ...buddy,
                source: "all-requests",
              }))
            : buddyRequestResponse.my_requests.map((buddy) => ({
                ...buddy,
                source: "my-requests",
              }));

        setBuddyData(processedData);

        return processedData;
      }
    } catch (error) {
      setLoading(false);
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
    }
  };

  async function fetchClubProfile() {
    try {
      const userResponse = await tdk.getOne("user", user_id, {});
      const clubResponse = await sdk.callRawAPI(
        `/v3/api/custom/courtmatchup/user/club/${userResponse.model.club_id}`,
        {},
        "GET"
      );
      setClubProfile(clubResponse.model);
      setUserProfile(userResponse.model);
      setClubSports(clubResponse.sports);

      // Check for default view setting in buddy_description
      let defaultView = "table"; // Default fallback
      if (clubResponse.model?.buddy_description) {
        try {
          const buddySettings = JSON.parse(
            clubResponse.model.buddy_description
          );
          if (buddySettings.default_view) {
            defaultView = buddySettings.default_view;
          }
        } catch (e) {
          console.error("Error parsing buddy_description:", e);
        }
      }

      // Set the active tab and finish initial loading
      setActiveTab(defaultView);
      setInitialLoading(false);
    } catch (error) {
      console.log("ERROR", error);
      // Even on error, we should set a default tab and finish loading
      setActiveTab("table");
      setInitialLoading(false);
    }
  }

  async function getAllBuddyData(filters = {}) {
    try {
      const allBuddyData = await fetchData(1, 10, filters);
      setAllBuddyData(allBuddyData);
    } catch (error) {
      console.log("ERROR", error);
      tokenExpireError(authDispatch, error.message);
    }
  }

  // Initial data loading
  useEffect(() => {
    // First fetch the club profile to determine the default view
    fetchClubProfile();

    // Set the path
    globalDispatch({
      type: "SETPATH",
      payload: {
        path: "find-a-buddy",
      },
    });
  }, []);

  // Fetch data when activeTab is set (after we know the default view)
  useEffect(() => {
    if (activeTab) {
      fetchData(1, 10, {});
      getAllBuddyData({});
    }
  }, [activeTab]);

  const handleRequestTabChange = (tab) => {
    setSelectedRequestTab(tab);

    fetchData(1, 10, {}, tab);
  };

  // Check if user has permission to access find a buddy
  if (user_permissions && !user_permissions.allowBuddy) {
    return (
      <AccessRestricted
        message={`Your current plan (${user_permissions?.planName}) does not include find a buddy feature. Please upgrade your plan to access this feature.`}
      />
    );
  }

  return (
    <>
      {(isLoading || !activeTab) && <LoadingSpinner />}

      {activeTab && (
        <>
          <div className="bg-white px-3 sm:px-4">
            <div className="flex flex-col pt-4 sm:flex-row sm:items-center sm:justify-between">
              <h1 className="mb-4 text-xl font-semibold sm:mb-6 sm:text-2xl">
                Find a buddy
              </h1>
              <div className="mb-4 flex flex-wrap items-center gap-2 sm:mb-0">
                <button
                  onClick={() => setIsSubscriptionModalOpen(true)}
                  className="rounded-xl border bg-green-900 px-3 py-2 text-xs text-white sm:px-4 sm:text-sm"
                >
                  Subscriptions
                </button>
                <Link
                  to={"/user/create-request"}
                  className="flex items-center rounded-xl border bg-primaryBlue px-3 py-2 text-xs text-white sm:text-sm"
                >
                  <svg
                    width="12"
                    height="12"
                    viewBox="0 0 12 12"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M5.25 5.25V0.75H6.75V5.25H11.25V6.75H6.75V11.25H5.25V6.75H0.75V5.25H5.25Z"
                      fill="white"
                    />
                  </svg>
                  <span className="ml-2">Create a request</span>
                </Link>
              </div>
            </div>

            <div className="flex flex-col pt-2 sm:flex-row sm:items-center sm:justify-between sm:pt-4">
              <div className="mb-3 flex max-w-fit overflow-x-auto text-xs sm:mb-0 sm:text-sm">
                {requestTabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => handleRequestTabChange(tab.id)}
                    className={`flex items-center gap-2 whitespace-nowrap bg-transparent px-3 py-2 sm:py-3 ${
                      selectedRequestTab === tab.id
                        ? "border-b-2 border-primaryBlue"
                        : ""
                    }`}
                  >
                    <span>{tab.label}</span>
                  </button>
                ))}
              </div>
              {/* Tabs */}
              <div className="mb-3 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:text-sm">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`whitespace-nowrap px-2 py-2 sm:px-3 ${
                      activeTab === tab.id
                        ? "bg-white-600"
                        : "bg-gray-100 text-gray-600"
                    }`}
                  >
                    {tab.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
          <div className="px-2 py-2 sm:px-3 sm:py-3">
            <div className="mx-auto max-w-7xl">
              {/* Tabs Content */}
              {activeTab === "table" && (
                <FindBuddyTableTab
                  fetchData={fetchData}
                  buddyData={buddyData}
                  setBuddyData={setBuddyData}
                  clubSports={clubSports}
                  selectedRequestTab={selectedRequestTab}
                />
              )}
              {activeTab === "calendar" && (
                <FindBuddyCalendarTab
                  fetchData={fetchData}
                  buddyData={buddyData}
                  setBuddyData={setBuddyData}
                  allBuddyData={allBuddyData}
                  clubSports={clubSports}
                  selectedRequestTab={selectedRequestTab}
                />
              )}
              {activeTab === "weekly" && (
                <FindBuddyWeeklyTab
                  fetchData={fetchData}
                  buddyData={buddyData}
                  setBuddyData={setBuddyData}
                  clubSports={clubSports}
                  selectedRequestTab={selectedRequestTab}
                />
              )}
            </div>
          </div>
          <SubscriptionsModal
            isOpen={isSubscriptionModalOpen}
            onClose={() => setIsSubscriptionModalOpen(false)}
            clubProfile={clubProfile}
            userProfile={userProfile}
            clubSports={clubSports}
          />
        </>
      )}
    </>
  );
}
