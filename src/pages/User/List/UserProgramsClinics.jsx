import React, {
  useEffect,
  useState,
  useContext,
  useMemo,
  useCallback,
} from "react";
import TreeSDK from "Utils/TreeSDK";
import TableTab from "Components/UserProgramClinicsTab/TableTab";
import CalendarTab from "Components/UserProgramClinicsTab/CalendarTab";
import WeeklyTab from "Components/UserProgramClinicsTab/WeeklyTab";
import { getManyByIds, GlobalContext, showToast } from "Context/Global";
import { tokenExpireError, AuthContext } from "Context/Auth";
import { useClub } from "Context/Club";
import MkdSDK from "Utils/MkdSDK";
import LoadingSpinner from "Components/LoadingSpinner";
import { IoChevronDown } from "react-icons/io5";

let tdk = new TreeSDK();
let sdk = new MkdSDK();

function FiltersContent({
  filters,
  setFilters,
  customFilters = [],
  programs = [],
  availableCategories = [],
  availableSubcategories = [],
  availableTags = [],
}) {
  const [expandedSections, setExpandedSections] = useState({
    dayOfWeek: true,
    timeOfDay: true,
    priceRange: true,
    categories: true,
    subcategories: true,
    tags: true,
    ...customFilters.reduce(
      (acc, filter) => ({
        ...acc,
        [`custom_${filter.id}`]: true,
      }),
      {}
    ),
  });

  const toggleSection = (section) => {
    setExpandedSections((prev) => ({
      ...prev,
      [section]: !prev[section],
    }));
  };

  const handleDayChange = (day) => {
    setFilters((prev) => ({
      ...prev,
      days: {
        ...prev.days,
        [day.toLowerCase()]: !prev.days[day.toLowerCase()],
      },
    }));
  };

  const handleTimeOfDayChange = (time) => {
    setFilters((prev) => ({
      ...prev,
      timeOfDay: {
        ...prev.timeOfDay,
        [time.toLowerCase()]: !prev.timeOfDay[time.toLowerCase()],
      },
    }));
  };

  const handlePriceChange = (type, value) => {
    setFilters((prev) => ({
      ...prev,
      price: {
        ...prev.price,
        [type]: value,
      },
    }));
  };

  const handleCustomFilterChange = (filterKey, optionValue) => {
    setFilters((prev) => ({
      ...prev,
      customFilters: {
        ...prev.customFilters,
        [filterKey]: prev.customFilters?.[filterKey]?.includes(optionValue)
          ? prev.customFilters[filterKey].filter((val) => val !== optionValue)
          : [...(prev.customFilters?.[filterKey] || []), optionValue],
      },
    }));
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Day of week */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("dayOfWeek")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Day of week</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.dayOfWeek ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.dayOfWeek && (
          <div className="space-y-3">
            {[
              "Weekend",
              "Weekday",
              "Sunday",
              "Monday",
              "Tuesday",
              "Wednesday",
              "Thursday",
              "Friday",
              "Saturday",
            ].map((day) => (
              <label key={day} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={filters.days[day.toLowerCase()]}
                  onChange={() => handleDayChange(day)}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">{day}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Time of day */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("timeOfDay")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Time of the day</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.timeOfDay ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.timeOfDay && (
          <div className="space-y-3">
            {["Morning", "Afternoon", "Evening"].map((time) => (
              <label key={time} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={filters.timeOfDay[time.toLowerCase()]}
                  onChange={() => handleTimeOfDayChange(time)}
                  className="h-4 w-4 rounded border-gray-300"
                />
                <span className="text-sm text-gray-700">{time}</span>
              </label>
            ))}
          </div>
        )}
      </div>

      {/* Price range */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("priceRange")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Price range</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.priceRange ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.priceRange && (
          <div className="flex gap-4">
            <div className="flex-1">
              <p className="mb-2 text-sm text-gray-600">From</p>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                  $
                </span>
                <input
                  type="number"
                  value={filters.price.from}
                  onChange={(e) => handlePriceChange("from", e.target.value)}
                  className="w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none"
                  placeholder="0.00"
                />
              </div>
            </div>
            <div className="flex-1">
              <p className="mb-2 text-sm text-gray-600">To</p>
              <div className="relative">
                <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">
                  $
                </span>
                <input
                  type="number"
                  value={filters.price.to}
                  onChange={(e) => handlePriceChange("to", e.target.value)}
                  className="w-full rounded-lg border border-gray-200 px-7 py-2 focus:border-blue-500 focus:outline-none"
                  placeholder="0.00"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Custom Filters */}
      {customFilters
        .filter((filter) => filter.enabled)
        .map((filter) => {
          // Get unique values for this filter from the programs data
          const uniqueValues = [
            ...new Set(
              programs
                .map((program) => program[filter.key])
                .filter(
                  (value) =>
                    value !== null && value !== undefined && value !== ""
                )
            ),
          ];

          if (uniqueValues.length === 0) return null;

          return (
            <div
              key={filter.id}
              className="space-y-3 rounded-xl border border-gray-200 bg-white p-5"
            >
              <button
                onClick={() => toggleSection(`custom_${filter.id}`)}
                className="flex w-full items-center justify-between"
              >
                <h3 className="text-base font-medium">{filter.label}</h3>
                <IoChevronDown
                  size={20}
                  className={`text-gray-400 transition-transform duration-200 ${
                    expandedSections[`custom_${filter.id}`] ? "rotate-180" : ""
                  }`}
                />
              </button>
              {expandedSections[`custom_${filter.id}`] && (
                <div className="space-y-3">
                  {uniqueValues.map((value) => (
                    <label key={value} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={
                          filters.customFilters?.[filter.key]?.includes(
                            value
                          ) || false
                        }
                        onChange={() =>
                          handleCustomFilterChange(filter.key, value)
                        }
                        className="h-4 w-4 rounded border-gray-300"
                      />
                      <span className="text-sm text-gray-700">
                        {filter.key === "recurring"
                          ? value === 1
                            ? "Yes"
                            : "No"
                          : value}
                      </span>
                    </label>
                  ))}
                </div>
              )}
            </div>
          );
        })}

      {/* Categories Filter */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("categories")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Categories</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.categories ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.categories && (
          <div className="space-y-3">
            {availableCategories.length === 0 ? (
              <p className="text-sm text-gray-500">
                No categories available. Create clinics with categories to see
                filter options.
              </p>
            ) : (
              availableCategories.map((category) => (
                <label key={category.id} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      filters.customFilters?.categories?.includes(
                        category.name
                      ) || false
                    }
                    onChange={() =>
                      handleCustomFilterChange("categories", category.name)
                    }
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">{category.name}</span>
                </label>
              ))
            )}
          </div>
        )}
      </div>

      {/* Subcategories Filter */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("subcategories")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Subcategories</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.subcategories ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.subcategories && (
          <div className="space-y-3">
            {availableSubcategories.length === 0 ? (
              <p className="text-sm text-gray-500">
                No subcategories available. Create clinics with subcategories to
                see filter options.
              </p>
            ) : (
              availableSubcategories.map((subcategory) => (
                <label key={subcategory.id} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      filters.customFilters?.subcategories?.includes(
                        subcategory.name
                      ) || false
                    }
                    onChange={() =>
                      handleCustomFilterChange(
                        "subcategories",
                        subcategory.name
                      )
                    }
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">
                    {subcategory.name}
                  </span>
                </label>
              ))
            )}
          </div>
        )}
      </div>

      {/* Tags Filter */}
      <div className="space-y-3 rounded-xl border border-gray-200 bg-white p-5">
        <button
          onClick={() => toggleSection("tags")}
          className="flex w-full items-center justify-between"
        >
          <h3 className="text-base font-medium">Tags</h3>
          <IoChevronDown
            size={20}
            className={`text-gray-400 transition-transform duration-200 ${
              expandedSections.tags ? "rotate-180" : ""
            }`}
          />
        </button>
        {expandedSections.tags && (
          <div className="space-y-3">
            {availableTags.length === 0 ? (
              <p className="text-sm text-gray-500">
                No tags available. Create clinics with tags to see filter
                options.
              </p>
            ) : (
              availableTags.map((tag) => (
                <label key={tag.id} className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      filters.customFilters?.tags?.includes(tag.name) || false
                    }
                    onChange={() => handleCustomFilterChange("tags", tag.name)}
                    className="h-4 w-4 rounded border-gray-300"
                  />
                  <span className="text-sm text-gray-700">{tag.name}</span>
                </label>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default function UserProgramsClinics() {
  const [activeTab, setActiveTab] = useState(null);
  const [sports, setSports] = useState([]);
  const [club, setClub] = useState(null);
  const [coaches, setCoaches] = useState([]);
  const [players, setPlayers] = useState([]);
  const { dispatch: globalDispatch } = useContext(GlobalContext);
  const { dispatch: authDispatch } = useContext(AuthContext);
  const { user_subscription, club_membership } = useClub();
  const [programs, setPrograms] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [customFilters, setCustomFilters] = useState([]);
  const [availableCategories, setAvailableCategories] = useState([]);
  const [availableSubcategories, setAvailableSubcategories] = useState([]);
  const [availableTags, setAvailableTags] = useState([]);

  // Get the user's membership plan
  const userMembershipPlan = useMemo(() => {
    if (!user_subscription?.planId || !club_membership?.length) return null;
    return club_membership.find(
      (plan) => plan.plan_id === user_subscription.planId
    );
  }, [user_subscription, club_membership]);
  const [filters, setFilters] = useState({
    days: {
      weekend: false,
      weekday: false,
      sunday: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
    },
    timeOfDay: {
      morning: false,
      afternoon: false,
      evening: false,
    },
    price: {
      from: "",
      to: "",
    },
    customFilters: {},
  });

  const tabs = [
    { id: "table", label: "Table" },
    { id: "calendar", label: "Calendar" },
    { id: "weekly", label: "Weekly" },
  ];

  const user_id = localStorage.getItem("user");

  const fetchSports = async () => {
    try {
      const userResponse = await tdk.getOne("user", user_id, {});
      const clubResponse = await tdk.getOne(
        "clubs",
        userResponse.model.club_id,
        {}
      );
      const sportsResponse = await tdk.getList("sports", {
        filter: [`club_id,eq,${userResponse.model.club_id}`],
      });
      console.log("sportsResponse", sportsResponse);
      setSports(sportsResponse.list);
      setClub(clubResponse.model);

      // Check for default view setting and custom filters in clinic_description
      let defaultView = "table"; // Default fallback
      if (clubResponse.model?.clinic_description) {
        try {
          const clinicSettings = JSON.parse(
            clubResponse.model.clinic_description
          );
          if (clinicSettings.default_view) {
            defaultView = clinicSettings.default_view;
          }
          if (clinicSettings.custom_filters) {
            setCustomFilters(clinicSettings.custom_filters);
          }
        } catch (e) {
          console.error("Error parsing clinic_description:", e);
        }
      }

      // Set the active tab
      setActiveTab(defaultView);
    } catch (error) {
      console.error(error);
      // Even on error, set a default tab
      setActiveTab("table");
    }
  };

  const fetchCoaches = async () => {
    const coachesResponse = await tdk.getList("coach", {
      join: [`user|user_id`],
    });
    const playersResponse = await tdk.getList("user", {
      filter: [`role,cs,user`],
    });
    setCoaches(coachesResponse.list);
    setPlayers(playersResponse.list);
  };

  const fetchCategoriesAndTags = async () => {
    try {
      if (!club?.id) return;

      // Fetch categories
      sdk.setTable("clinic_categories");
      const categoriesResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club.id}`],
        },
        "GETALL"
      );

      // Fetch subcategories
      sdk.setTable("clinic_subcategories");
      const subcategoriesResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club.id}`],
        },
        "GETALL"
      );

      // Fetch tags
      sdk.setTable("clinic_tags");
      const tagsResponse = await sdk.callRestAPI(
        {
          filter: [`club_id,eq,${club.id}`],
        },
        "GETALL"
      );

      setAvailableCategories(categoriesResponse.list || []);
      setAvailableSubcategories(subcategoriesResponse.list || []);
      setAvailableTags(tagsResponse.list || []);
    } catch (error) {
      console.error(
        "Error fetching categories, subcategories and tags:",
        error
      );
      // If tables don't exist, we'll just have empty arrays
      setAvailableCategories([]);
      setAvailableSubcategories([]);
      setAvailableTags([]);
    }
  };

  const fetchClinics = async (date, skipFilters = false, filterString = "") => {
    setLoading(true);
    try {
      let url = `/v3/api/custom/courtmatchup/user/clinics`;

      // If filterString is provided, use it directly
      if (filterString) {
        url += `?${filterString}`;
      } else {
        let filterParams = [];

        if (!skipFilters) {
          // Handle date filters
          if (date && !isNaN(new Date(date).getTime())) {
            const startDate = new Date(date);
            startDate.setHours(0, 0, 0, 0);

            const endDate = new Date(startDate);
            endDate.setDate(endDate.getDate() + 6);
            endDate.setHours(23, 59, 59, 999);

            filterParams.push(
              `start_date=${startDate.toISOString().split("T")[0]}`
            );
            filterParams.push(
              `clinic_end_date=${endDate.toISOString().split("T")[0]}`
            );
          }

          // Handle day filters
          const selectedDays = Object.entries(filters.days)
            .filter(([_, isSelected]) => isSelected)
            .map(([day]) => day.toLowerCase());

          if (selectedDays.length > 0) {
            filterParams.push(`weekday=${selectedDays.join(",")}`);
          }

          // Handle time of day filters
          const selectedTimes = Object.entries(filters.timeOfDay)
            .filter(([_, isSelected]) => isSelected)
            .map(([time]) => time.toLowerCase());

          if (selectedTimes.length > 0) {
            filterParams.push(`times=${selectedTimes.join(",")}`);
          }

          // Handle custom filters
          if (filters.customFilters) {
            Object.entries(filters.customFilters).forEach(
              ([filterKey, selectedValues]) => {
                if (selectedValues && selectedValues.length > 0) {
                  // Handle categories, subcategories, and tags with names
                  if (filterKey === "categories") {
                    filterParams.push(`category=${selectedValues.join(",")}`);
                  } else if (filterKey === "subcategories") {
                    filterParams.push(
                      `subcategory=${selectedValues.join(",")}`
                    );
                  } else if (filterKey === "tags") {
                    filterParams.push(`tag=${selectedValues.join(",")}`);
                  } else {
                    // Handle other custom filters as before
                    filterParams.push(
                      `${filterKey}=${selectedValues.join(",")}`
                    );
                  }
                }
              }
            );
          }
        }

        // Always add sport filtering based on user's membership plan (even when skipFilters is true)
        if (userMembershipPlan?.applicable_sports?.length > 0) {
          filterParams.push(
            `sport_ids=${userMembershipPlan.applicable_sports.join(",")}`
          );
        }

        // Add default week=0 if no other parameters
        if (filterParams.length === 0) {
          filterParams.push("week=0");
        }

        // Combine all filter parameters
        if (filterParams.length > 0) {
          url += `?${filterParams.join("&")}`;
        }
      }

      const response = await sdk.callRawAPI(url, {}, "GET");

      if (!response.error && response?.programs) {
        setPrograms(response.programs);
      }
    } catch (error) {
      console.log(error);
      showToast(globalDispatch, error.message, "3000", "error");
      tokenExpireError(authDispatch, error.status);
    } finally {
      setLoading(false);
    }
  };

  // Function to fetch coaches for a specific clinic
  const fetchCoachesForClinic = useCallback(
    async (coachIds) => {
      try {
        if (!coachIds || !Array.isArray(coachIds) || coachIds.length === 0) {
          return [];
        }

        const coachResponse = await getManyByIds(
          globalDispatch,
          authDispatch,
          "coach",
          coachIds,
          "user|user_id"
        );

        if (coachResponse.list && Array.isArray(coachResponse.list)) {
          return coachResponse.list;
        }

        return [];
      } catch (error) {
        console.error("Error fetching coaches for clinic:", error);
        return [];
      }
    },
    [globalDispatch, authDispatch]
  );

  const clearFilters = async () => {
    // Reset filters state
    setFilters({
      days: {
        weekend: false,
        weekday: false,
        sunday: false,
        monday: false,
        tuesday: false,
        wednesday: false,
        thursday: false,
        friday: false,
        saturday: false,
      },
      timeOfDay: {
        morning: false,
        afternoon: false,
        evening: false,
      },
      price: {
        from: "",
        to: "",
      },
      customFilters: {},
    });

    // Reset API without any filters (but sport filtering will still be applied in fetchClinics)
    await fetchClinics(null, true);
  };

  useEffect(() => {
    (async () => {
      setLoading(true);
      // First fetch the club profile to determine the default view
      await fetchSports();
      await fetchCoaches();
      await fetchCategoriesAndTags();
      setLoading(false);

      // Set the path
      globalDispatch({
        type: "SETPATH",
        payload: {
          path: "program-clinics",
        },
      });
    })();
  }, []);

  // Fetch clinics when activeTab is set (after we know the default view)
  useEffect(() => {
    if (activeTab) {
      (async () => {
        setLoading(true);
        await fetchClinics();
        setLoading(false);
      })();
    }
  }, [activeTab]);

  // Fetch categories and tags when club changes
  useEffect(() => {
    if (club?.id) {
      fetchCategoriesAndTags();
    }
  }, [club?.id]);

  console.log("programs", programs);

  async function handleClickTab(tab) {
    // Reset filters when switching tabs
    await clearFilters();
    setActiveTab(tab);
  }

  return (
    <>
      {(isLoading || !activeTab) && <LoadingSpinner />}

      {activeTab && (
        <>
          <div className="flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4">
            <h1 className="mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl">
              Clinics
            </h1>

            {/* Tabs */}
            <div className="mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleClickTab(tab.id)}
                  className={`whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2 ${
                    activeTab === tab.id
                      ? "bg-white-600"
                      : "bg-gray-100 text-gray-600"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </div>
          <div className="px-2 py-2 sm:px-3 sm:py-3">
            <div className="mx-auto max-w-7xl">
              {/* Tabs Content */}
              {activeTab === "table" && (
                <TableTab
                  programs={programs}
                  fetchClinics={fetchClinics}
                  FiltersContent={(props) => (
                    <FiltersContent
                      {...props}
                      customFilters={customFilters}
                      programs={programs}
                      availableCategories={availableCategories}
                      availableSubcategories={availableSubcategories}
                      availableTags={availableTags}
                    />
                  )}
                  filters={filters}
                  setFilters={setFilters}
                  clearFilters={clearFilters}
                  fetchCoachesForClinic={fetchCoachesForClinic}
                />
              )}
              {activeTab === "calendar" && (
                <CalendarTab
                  programs={programs}
                  fetchClinics={fetchClinics}
                  FiltersContent={(props) => (
                    <FiltersContent
                      {...props}
                      customFilters={customFilters}
                      programs={programs}
                      availableCategories={availableCategories}
                      availableSubcategories={availableSubcategories}
                      availableTags={availableTags}
                    />
                  )}
                  filters={filters}
                  setFilters={setFilters}
                  clubProfile={club}
                  clearFilters={clearFilters}
                  fetchCoachesForClinic={fetchCoachesForClinic}
                />
              )}
              {activeTab === "weekly" && (
                <WeeklyTab
                  programs={programs}
                  fetchClinics={fetchClinics}
                  FiltersContent={(props) => (
                    <FiltersContent
                      {...props}
                      customFilters={customFilters}
                      programs={programs}
                      availableCategories={availableCategories}
                      availableSubcategories={availableSubcategories}
                      availableTags={availableTags}
                    />
                  )}
                  filters={filters}
                  setFilters={setFilters}
                  clearFilters={clearFilters}
                  fetchCoachesForClinic={fetchCoachesForClinic}
                />
              )}
            </div>
          </div>
        </>
      )}
    </>
  );
}
