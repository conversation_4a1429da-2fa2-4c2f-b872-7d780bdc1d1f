export default function ClinicPerspective({ club }) {
  const clinicDescription = club?.clinic_description
    ? JSON.parse(club?.clinic_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-indigo-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-indigo-900">
          User Clinic & Program Experience
        </h3>
        <p className="text-indigo-800">
          This shows how users experience browsing and booking group clinics and
          programs.
        </p>
      </div>

      {/* Main Interface */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <div className="mb-6 flex flex-col justify-between sm:flex-row sm:items-center">
          <h4 className="mb-3 text-xl font-semibold sm:mb-0">Clinics</h4>

          {/* View Tabs */}
          <div className="flex max-w-fit divide-x overflow-x-auto rounded-xl border text-sm">
            <button className="whitespace-nowrap bg-white px-3 py-2 font-medium">
              Table
            </button>
            <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
              Calendar
            </button>
            <button className="whitespace-nowrap bg-gray-100 px-3 py-2 text-gray-600">
              Weekly
            </button>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-4">
          {/* Filter Button */}
          <div className="lg:col-span-1">
            <button className="flex w-full items-center justify-center gap-2 rounded-lg border border-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50">
              <svg
                className="h-4 w-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z"
                />
              </svg>
              Filters
            </button>
          </div>

          {/* Week Navigation */}
          <div className="lg:col-span-2">
            <div className="flex items-center justify-center gap-3">
              <button className="rounded p-2 hover:bg-gray-100">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="text-sm font-medium">Dec 15 - Dec 21, 2024</span>
              <button className="rounded p-2 hover:bg-gray-100">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>

          {/* Sort and Available Only */}
          <div className="flex gap-2 lg:col-span-1">
            <div className="flex items-center">
              <input
                type="checkbox"
                className="h-4 w-4 rounded border-gray-300 text-indigo-600"
              />
              <label className="ml-2 text-sm text-gray-700">
                Available only
              </label>
            </div>
            <button className="flex items-center gap-1 rounded border border-gray-300 px-3 py-1 text-sm">
              <span>Sort</span>
              <svg
                className="h-3 w-3"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>

      {/* Clinic Cards */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Available Clinics
        </h4>

        <div className="space-y-4">
          {/* Available Clinic */}
          <div className="cursor-pointer rounded-lg bg-gray-50 p-4 transition-colors duration-200 hover:bg-gray-100">
            <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-2">
                  <h3 className="text-lg font-medium">Tennis Fundamentals</h3>
                  <span className="rounded border border-indigo-200 bg-indigo-100 px-2 py-1 text-xs text-indigo-700">
                    Beginner
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-600">
                  December 18, 2024 • 10:00 AM - 11:30 AM
                </p>
                <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-gray-500">
                  <span>Coach: John Smith</span>
                  <span>•</span>
                  <span>Duration: 1.5 hours</span>
                  <span>•</span>
                  <span>Cost: $45 per person</span>
                </div>
              </div>
              <div className="flex flex-col gap-2 sm:items-end">
                <span className="w-fit rounded-full border border-green-600 bg-green-50 px-3 py-1 text-xs text-green-600">
                  Slots available: 3 (out of 8)
                </span>
                <button className="rounded bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700">
                  Register
                </button>
              </div>
            </div>
          </div>

          {/* Registered Clinic */}
          <div className="cursor-pointer rounded-lg border border-blue-200 bg-blue-50 p-4 transition-colors duration-200 hover:bg-blue-100">
            <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-2">
                  <h3 className="text-lg font-medium text-blue-900">
                    Advanced Serve Clinic
                  </h3>
                  <span className="rounded bg-blue-600 px-2 py-1 text-xs text-white">
                    REGISTERED
                  </span>
                  <span className="rounded border border-orange-200 bg-orange-100 px-2 py-1 text-xs text-orange-700">
                    Advanced
                  </span>
                </div>
                <p className="mt-1 text-sm text-blue-700">
                  December 20, 2024 • 2:00 PM - 3:30 PM
                </p>
                <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-blue-600">
                  <span>Coach: Maria Brown</span>
                  <span>•</span>
                  <span>Duration: 1.5 hours</span>
                  <span>•</span>
                  <span>Cost: $65 per person</span>
                </div>
              </div>
              <div className="flex flex-col gap-2 sm:items-end">
                <span className="w-fit rounded-full border border-blue-600 bg-blue-100 px-3 py-1 text-xs text-blue-700">
                  You are registered
                </span>
                <button className="rounded bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700">
                  View Details
                </button>
              </div>
            </div>
          </div>

          {/* Full Clinic */}
          <div className="cursor-pointer rounded-lg bg-gray-50 p-4 opacity-75 transition-colors duration-200 hover:bg-gray-100">
            <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-2">
                  <h3 className="text-lg font-medium text-gray-700">
                    Doubles Strategy Workshop
                  </h3>
                  <span className="rounded border border-yellow-200 bg-yellow-100 px-2 py-1 text-xs text-yellow-700">
                    Intermediate
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-600">
                  December 22, 2024 • 4:00 PM - 5:30 PM
                </p>
                <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-gray-500">
                  <span>Coach: David Wilson</span>
                  <span>•</span>
                  <span>Duration: 1.5 hours</span>
                  <span>•</span>
                  <span>Cost: $55 per person</span>
                </div>
              </div>
              <div className="flex flex-col gap-2 sm:items-end">
                <span className="w-fit rounded-full border border-red-600 bg-red-50 px-3 py-1 text-xs text-red-600">
                  No slots available
                </span>
                <button
                  className="cursor-not-allowed rounded bg-gray-400 px-4 py-2 text-sm font-medium text-white"
                  disabled
                >
                  Full
                </button>
              </div>
            </div>
          </div>

          {/* Multi-day Program */}
          <div className="cursor-pointer rounded-lg border border-purple-200 bg-purple-50 p-4 transition-colors duration-200 hover:bg-purple-100">
            <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
              <div className="flex-1">
                <div className="flex flex-wrap items-center gap-2">
                  <h3 className="text-lg font-medium text-purple-900">
                    Junior Tennis Program
                  </h3>
                  <span className="rounded bg-purple-600 px-2 py-1 text-xs text-white">
                    PROGRAM
                  </span>
                  <span className="rounded border border-green-200 bg-green-100 px-2 py-1 text-xs text-green-700">
                    Youth
                  </span>
                </div>
                <p className="mt-1 text-sm text-purple-700">
                  December 23, 2024 - January 15, 2025 • Mondays & Wednesdays •
                  4:00 PM - 5:00 PM
                </p>
                <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-purple-600">
                  <span>Coach: Sarah Johnson</span>
                  <span>•</span>
                  <span>8 sessions</span>
                  <span>•</span>
                  <span>Cost: $240 per person</span>
                </div>
              </div>
              <div className="flex flex-col gap-2 sm:items-end">
                <span className="w-fit rounded-full border border-green-600 bg-green-50 px-3 py-1 text-xs text-green-600">
                  Slots available: 5 (out of 12)
                </span>
                <button className="rounded bg-purple-600 px-4 py-2 text-sm font-medium text-white hover:bg-purple-700">
                  Register
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Clinic Details Modal Preview */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Clinic Registration View
        </h4>

        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Clinic Information */}
          <div className="rounded-lg border border-gray-200 p-4">
            <div className="mb-4">
              <div className="mb-2 flex items-center gap-2">
                <h3 className="text-xl font-semibold text-gray-900">
                  Tennis Fundamentals
                </h3>
                <span className="rounded border border-indigo-200 bg-indigo-100 px-2 py-1 text-xs text-indigo-700">
                  Beginner
                </span>
              </div>
              <p className="text-gray-600">
                Perfect for players new to tennis or looking to improve basic
                techniques.
              </p>
            </div>

            <div className="space-y-3 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Date:</span>
                <span className="font-medium">December 18, 2024</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Time:</span>
                <span className="font-medium">10:00 AM - 11:30 AM</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Duration:</span>
                <span className="font-medium">1.5 hours</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Coach:</span>
                <span className="font-medium">John Smith</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Location:</span>
                <span className="font-medium">Courts 1-2</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Skill Level:</span>
                <span className="font-medium">Beginner (NTRP 2.0-3.0)</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Available Spots:</span>
                <span className="font-medium text-green-600">3 out of 8</span>
              </div>
            </div>

            <div className="mt-4 border-t border-gray-200 pt-4">
              <div className="mb-2 text-sm font-medium text-gray-700">
                What You'll Learn:
              </div>
              <ul className="space-y-1 text-sm text-gray-600">
                <li>• Proper grip and stance</li>
                <li>• Basic forehand and backhand strokes</li>
                <li>• Serving fundamentals</li>
                <li>• Court positioning and movement</li>
              </ul>
            </div>

            <div className="mt-4 border-t border-gray-200 pt-4">
              <div className="mb-2 text-sm font-medium text-gray-700">
                What to Bring:
              </div>
              <p className="text-sm text-gray-600">
                Tennis racket (loaners available), comfortable athletic wear,
                tennis shoes, and water bottle.
              </p>
            </div>
          </div>

          {/* Registration Form */}
          <div className="rounded-lg border border-gray-200 p-4">
            <h5 className="mb-3 font-medium text-gray-900">
              Register for Clinic
            </h5>

            <div className="space-y-4">
              {/* Player Selection */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Select Participants
                </label>
                <div className="space-y-2">
                  <div className="flex items-center space-x-3 rounded border border-indigo-200 bg-indigo-50 p-2">
                    <input
                      type="checkbox"
                      checked
                      className="h-4 w-4 text-indigo-600"
                    />
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-sm font-medium text-white">
                      YU
                    </div>
                    <div>
                      <div className="text-sm font-medium">You</div>
                      <div className="text-xs text-gray-600">NTRP: 2.5</div>
                    </div>
                  </div>

                  <div className="flex items-center space-x-3 rounded border border-gray-200 p-2 hover:bg-gray-50">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-indigo-600"
                    />
                    <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-400 text-sm font-medium text-white">
                      FM
                    </div>
                    <div>
                      <div className="text-sm font-medium">Family Member</div>
                      <div className="text-xs text-gray-600">NTRP: 2.0</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Special Requirements */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700">
                  Special Requirements (Optional)
                </label>
                <textarea
                  className="w-full rounded border border-gray-300 px-3 py-2 text-sm"
                  rows="3"
                  placeholder="Any special accommodations or notes..."
                ></textarea>
              </div>

              {/* Pricing Breakdown */}
              <div className="rounded bg-gray-50 p-3">
                <div className="mb-2 text-sm font-medium text-gray-900">
                  Registration Summary
                </div>
                <div className="space-y-1 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">
                      Clinic Fee (1 participant):
                    </span>
                    <span>$45.00</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Service Fee:</span>
                    <span>$4.50</span>
                  </div>
                  <div className="flex justify-between border-t border-gray-300 pt-1 font-medium">
                    <span>Total:</span>
                    <span>$49.50</span>
                  </div>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="flex items-start space-x-2">
                <input
                  type="checkbox"
                  className="mt-0.5 h-4 w-4 text-indigo-600"
                />
                <label className="text-xs text-gray-600">
                  I agree to the clinic cancellation policy and understand that
                  refunds are only available 24 hours before the clinic start
                  time.
                </label>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button className="flex-1 rounded bg-gray-300 px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-400">
                  Cancel
                </button>
                <button className="flex-1 rounded bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700">
                  Register & Pay
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Calendar and Weekly Views Preview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Calendar View */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h4 className="mb-4 text-lg font-medium text-gray-900">
            Calendar View
          </h4>

          <div className="rounded-lg border p-4">
            <div className="mb-4 flex items-center justify-between">
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="font-medium">December 2024</span>
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 text-sm">
              <div className="py-2 text-center font-medium text-gray-500">
                S
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                M
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                T
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                W
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                T
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                F
              </div>
              <div className="py-2 text-center font-medium text-gray-500">
                S
              </div>

              {/* Sample dates with clinics */}
              <div className="py-2 text-center text-gray-400">1</div>
              <div className="py-2 text-center text-gray-400">2</div>
              <div className="py-2 text-center text-gray-400">3</div>
              <div className="py-2 text-center text-gray-400">4</div>
              <div className="py-2 text-center text-gray-400">5</div>
              <div className="py-2 text-center text-gray-400">6</div>
              <div className="py-2 text-center text-gray-400">7</div>
              <div className="py-2 text-center text-gray-400">8</div>
              <div className="py-2 text-center text-gray-400">9</div>
              <div className="py-2 text-center text-gray-400">10</div>
              <div className="py-2 text-center text-gray-400">11</div>
              <div className="py-2 text-center text-gray-400">12</div>
              <div className="py-2 text-center text-gray-400">13</div>
              <div className="py-2 text-center text-gray-400">14</div>
              <div className="py-2 text-center text-gray-400">15</div>
              <div className="py-2 text-center text-gray-400">16</div>
              <div className="py-2 text-center text-gray-400">17</div>
              <div className="relative rounded bg-indigo-100 py-2 text-center text-indigo-800">
                18
                <div className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-indigo-600"></div>
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                19
              </div>
              <div className="relative rounded bg-blue-100 py-2 text-center text-blue-800">
                20
                <div className="absolute bottom-0 left-1/2 h-1 w-1 -translate-x-1/2 transform rounded-full bg-blue-600"></div>
              </div>
              <div className="cursor-pointer py-2 text-center hover:bg-gray-100">
                21
              </div>
            </div>

            <div className="mt-4 flex items-center gap-4 text-xs">
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-indigo-600"></div>
                <span>Available clinics</span>
              </div>
              <div className="flex items-center gap-1">
                <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                <span>Registered</span>
              </div>
            </div>
          </div>
        </div>

        {/* Weekly View */}
        <div className="rounded-lg bg-white p-6 shadow-sm">
          <h4 className="mb-4 text-lg font-medium text-gray-900">
            Weekly View
          </h4>

          <div className="rounded-lg border p-4">
            <div className="mb-4 flex items-center justify-between">
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>
              <span className="font-medium">Dec 15 - Dec 21, 2024</span>
              <button className="rounded p-1 hover:bg-gray-100">
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>

            {/* Weekly Grid */}
            <div className="space-y-2">
              {/* Monday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Mon 16
                </div>
                <div className="flex-1 text-xs text-gray-500">No clinics</div>
              </div>

              {/* Tuesday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Tue 17
                </div>
                <div className="flex-1 text-xs text-gray-500">No clinics</div>
              </div>

              {/* Wednesday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Wed 18
                </div>
                <div className="flex-1">
                  <div className="flex h-6 items-center rounded bg-indigo-100 px-2 text-xs text-indigo-800">
                    10:00 AM - Tennis Fundamentals
                  </div>
                </div>
              </div>

              {/* Thursday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Thu 19
                </div>
                <div className="flex-1 text-xs text-gray-500">No clinics</div>
              </div>

              {/* Friday */}
              <div className="flex items-center gap-3 rounded p-2 hover:bg-gray-50">
                <div className="w-16 text-sm font-medium text-gray-700">
                  Fri 20
                </div>
                <div className="flex-1">
                  <div className="flex h-6 items-center rounded bg-blue-100 px-2 text-xs text-blue-800">
                    2:00 PM - Advanced Serve (Registered)
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Summary */}
      <div className="rounded-lg bg-white p-6 shadow-sm">
        <h4 className="mb-4 text-lg font-medium text-gray-900">
          Clinic & Program Features
        </h4>

        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div className="rounded-lg border border-indigo-200 bg-indigo-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-600 text-sm font-medium text-white">
                1
              </div>
              <h5 className="font-medium text-indigo-900">Browse & Filter</h5>
            </div>
            <div className="space-y-2 text-sm text-indigo-800">
              <p>• View clinics by sport and skill level</p>
              <p>• Filter by date, time, and availability</p>
              <p>• Multiple view options: Table, Calendar, Weekly</p>
              <p>• Search by category and tags</p>
            </div>
          </div>

          <div className="rounded-lg border border-green-200 bg-green-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-600 text-sm font-medium text-white">
                2
              </div>
              <h5 className="font-medium text-green-900">Register & Learn</h5>
            </div>
            <div className="space-y-2 text-sm text-green-800">
              <p>• Detailed clinic information and curriculum</p>
              <p>• Professional coach profiles and expertise</p>
              <p>• Multiple participant registration</p>
              <p>• Clear pricing and cancellation policies</p>
            </div>
          </div>

          <div className="rounded-lg border border-purple-200 bg-purple-50 p-4">
            <div className="mb-3 flex items-center space-x-3">
              <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-600 text-sm font-medium text-white">
                3
              </div>
              <h5 className="font-medium text-purple-900">Track Progress</h5>
            </div>
            <div className="space-y-2 text-sm text-purple-800">
              <p>• View registered clinics and programs</p>
              <p>• Calendar integration and reminders</p>
              <p>• Multi-session program tracking</p>
              <p>• Skill development progression</p>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Messages */}
      {clinicDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.reservation_description}
          </p>
        </div>
      )}

      {clinicDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
