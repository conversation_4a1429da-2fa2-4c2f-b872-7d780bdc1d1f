export default function ClinicPerspective({ club }) {
  const clinicDescription = club?.clinic_description
    ? JSON.parse(club?.clinic_description)
    : {
        reservation_description: "",
        payment_description: "",
      };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="rounded-lg bg-indigo-50 p-4">
        <h3 className="mb-2 text-lg font-semibold text-indigo-900">
          User Clinic & Program Experience
        </h3>
        <p className="text-indigo-800">
          This shows how users experience browsing and booking group clinics and
          programs.
        </p>
      </div>

      {/* Exact UI Copy - Header */}
      <div className="flex flex-col justify-between bg-white px-3 py-3 sm:flex-row sm:items-center sm:px-4 sm:py-4">
        <h1 className="mb-3 text-xl font-semibold sm:mb-6 sm:text-2xl">
          Clinics
        </h1>

        {/* Tabs */}
        <div className="mb-4 flex max-w-fit divide-x overflow-x-auto rounded-xl border text-xs sm:mb-8 sm:text-sm">
          <button className="bg-white-600 whitespace-nowrap px-2 py-1.5 sm:px-3 sm:py-2">
            Table
          </button>
          <button className="whitespace-nowrap bg-gray-100 px-2 py-1.5 text-gray-600 sm:px-3 sm:py-2">
            Calendar
          </button>
          <button className="whitespace-nowrap bg-gray-100 px-2 py-1.5 text-gray-600 sm:px-3 sm:py-2">
            Weekly
          </button>
        </div>
      </div>

      {/* Exact UI Copy - Content */}
      <div className="px-2 py-2 sm:px-3 sm:py-3">
        <div className="mx-auto max-w-7xl">
          <div className="mx-auto mt-3 max-w-4xl rounded-lg bg-white p-3 shadow-sm sm:mt-5 sm:p-4">
            {/* Filter Header - Exact Copy */}
            <div className="mb-4 flex flex-col justify-between gap-3 sm:mb-6 sm:flex-row sm:items-center sm:gap-0">
              {/* Filter Button and Clear All */}
              <div className="flex items-center gap-3 sm:gap-4">
                <button className="flex items-center gap-2 rounded-xl border border-gray-200 bg-white px-3 py-1.5 sm:px-4 sm:py-2">
                  <svg
                    className="text-blue-600"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                  </svg>
                  <span className="text-sm text-gray-700 sm:text-base">
                    Filter
                  </span>
                  <span className="flex h-5 w-5 items-center justify-center rounded-full bg-blue-600 text-xs text-white">
                    2
                  </span>
                </button>
                <button className="text-sm text-gray-500 hover:underline sm:text-base">
                  Clear all
                </button>
              </div>

              {/* Available Slots and Sort Options */}
              <div className="mt-3 flex flex-wrap items-center gap-3 sm:mt-0 sm:gap-4">
                {/* Available Slots Toggle */}
                <div className="flex items-center gap-2">
                  <span className="whitespace-nowrap text-xs text-gray-700 sm:text-sm">
                    Available slot only
                  </span>
                  <button className="relative h-5 w-10 rounded-full bg-blue-600 transition-colors duration-200 ease-in-out sm:h-6 sm:w-12">
                    <div className="absolute top-0.5 h-4 w-4 translate-x-5 transform rounded-full bg-white shadow-md transition-transform duration-200 ease-in-out sm:h-5 sm:w-5 sm:translate-x-6" />
                  </button>
                </div>

                {/* Sort Dropdown */}
                <div className="relative border-gray-200 sm:border-l sm:pl-4">
                  <button className="flex items-center gap-1 rounded-xl border border-gray-200 bg-white px-2 py-1.5 text-xs sm:gap-2 sm:px-4 sm:py-2 sm:text-sm">
                    <span className="whitespace-nowrap text-gray-700">
                      By date (Latest)
                    </span>
                    <svg
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-gray-400"
                    >
                      <polyline points="6 9 12 15 18 9"></polyline>
                    </svg>
                  </button>
                </div>
              </div>
            </div>

            {/* Clinic Cards - Exact Copy */}
            <div className="space-y-3 sm:space-y-4">
              {/* Available Clinic */}
              <div className="cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4">
                <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2">
                      <h3 className="text-base font-medium sm:text-lg">
                        Tennis Fundamentals
                      </h3>
                    </div>
                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">
                      December 18, 2024 • 10:00 AM - 11:30 AM
                    </p>
                  </div>
                  <div className="flex flex-col gap-2 sm:items-end sm:gap-4">
                    <span className="w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]">
                      Slots available: 3 (out of 8)
                    </span>
                  </div>
                </div>
              </div>

              {/* Registered Clinic */}
              <div className="cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4">
                <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2">
                      <h3 className="text-base font-medium sm:text-lg">
                        Advanced Serve Clinic
                      </h3>
                      <span className="rounded bg-blue-600 px-2 py-1 text-xs text-white">
                        REGISTERED
                      </span>
                    </div>
                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">
                      December 20, 2024 • 2:00 PM - 3:30 PM
                    </p>
                  </div>
                  <div className="flex flex-col gap-2 sm:items-end sm:gap-4">
                    <span className="w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]">
                      Slots available: 2 (out of 6)
                    </span>
                  </div>
                </div>
              </div>

              {/* Full Clinic */}
              <div className="cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4">
                <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2">
                      <h3 className="text-base font-medium sm:text-lg">
                        Doubles Strategy Workshop
                      </h3>
                    </div>
                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">
                      December 22, 2024 • 4:00 PM - 5:30 PM
                    </p>
                  </div>
                  <div className="flex flex-col gap-2 sm:items-end sm:gap-4">
                    <span className="w-fit rounded-full border border-red-800 bg-red-50 px-3 py-1 text-xs text-red-800">
                      No slots available
                    </span>
                  </div>
                </div>
              </div>

              {/* Multi-day Program */}
              <div className="cursor-pointer rounded-lg bg-gray-50 p-3 transition-colors duration-200 hover:bg-gray-100 sm:p-4">
                <div className="flex flex-col justify-between gap-3 sm:flex-row sm:items-center sm:gap-0">
                  <div className="flex-1">
                    <div className="flex flex-wrap items-center gap-2">
                      <h3 className="text-base font-medium sm:text-lg">
                        Junior Tennis Program
                      </h3>
                    </div>
                    <p className="mt-1 text-xs text-gray-600 sm:text-sm">
                      December 23, 2024 - January 15, 2025 • Mondays &
                      Wednesdays • 4:00 PM - 5:00 PM
                    </p>
                  </div>
                  <div className="flex flex-col gap-2 sm:items-end sm:gap-4">
                    <span className="w-fit rounded-full border border-[#176448] bg-green-50 px-3 py-1 text-xs text-[#176448]">
                      Slots available: 5 (out of 12)
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Custom Messages */}
      {clinicDescription.reservation_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Reservation Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.reservation_description}
          </p>
        </div>
      )}

      {clinicDescription.payment_description && (
        <div className="rounded-lg bg-gray-50 p-4">
          <h4 className="mb-2 font-medium text-gray-900">
            Custom Payment Message
          </h4>
          <p className="text-sm text-gray-700">
            {clinicDescription.payment_description}
          </p>
        </div>
      )}
    </div>
  );
}
